<PERSON>L<PERSON> BANKED LINKER/LOCATER V6.22.4.0                                                    06/02/2025  19:35:43  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22.4.0, INVOKED BY:
E:\KEIL5\C51\BIN\BL51.EXE .\Objects\ds1302.obj, .\Objects\filtering.obj, .\Objects\iic.obj, .\Objects\init.obj, .\Object
>> s\key.obj, .\Objects\led.obj, .\Objects\onewire.obj, .\Objects\seg.obj, .\Objects\uart.obj, .\Objects\ultrasound.obj,
>>  .\Objects\main.obj, .\Objects\STARTUP.obj TO .\Objects\demo_micu PRINT (.\Listings\demo_micu.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\ds1302.obj (DS1302)
  .\Objects\filtering.obj (FILTERING)
  .\Objects\iic.obj (IIC)
  .\Objects\init.obj (INIT)
  .\Objects\key.obj (KEY)
  .\Objects\led.obj (LED)
  .\Objects\onewire.obj (ONEWIRE)
  .\Objects\seg.obj (SEG)
  .\Objects\uart.obj (UART)
  .\Objects\ultrasound.obj (ULTRASOUND)
  .\Objects\main.obj (MAIN)
  .\Objects\STARTUP.obj (?C_STARTUP)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FPADD)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FPDIV)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FPCMP)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?CASTF)
  E:\KEIL5\C51\LIB\C51FPS.LIB (PRINTF)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FPROUND)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FPCONVERT)
  E:\KEIL5\C51\LIB\C51FPS.LIB (?C?FTNPWR)
  E:\KEIL5\C51\LIB\C51S.LIB (?C_INIT)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?IMUL)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?UIDIV)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?SLDIV)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?ULCMP)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?LLDIDATA)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?LLDIDATA0)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?LSTIDATA)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?LSTPDATA)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?PLDIIDATA)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?CCASE)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?ICALL)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?MEMSET)
  E:\KEIL5\C51\LIB\C51S.LIB (?C?ULDIV)


LINK MAP OF MODULE:  .\Objects\demo_micu (DS1302)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 2


            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            IDATA   0008H     0004H     UNIT         ?ID?LED
                    000CH     0014H                  *** GAP ***
            BIT     0020H.0   0001H.1   UNIT         _BIT_GROUP_
            BIT     0021H.1   0000H.1   UNIT         ?BI?BEEP?LED
            BIT     0021H.2   0000H.1   UNIT         ?BI?RELAY?LED
            BIT     0021H.3   0000H.1   UNIT         ?BI?MOTOR?LED
                    0021H.4   0000H.4                *** GAP ***
            DATA    0022H     001CH     UNIT         _DATA_GROUP_
            IDATA   003EH     0087H     UNIT         ?ID?MAIN
            IDATA   00C5H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
            XDATA   0000H     0021H     INPAGE       ?PD?FILTERING
            XDATA   0021H     001AH     INPAGE       ?PD?MAIN

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0018H     UNIT         ?PR?_I2CSENDACK?IIC
            CODE    001BH     0003H     ABSOLUTE     
            CODE    001EH     0005H     UNIT         ?PR?SCHEDULER_INIT?MAIN
            CODE    0023H     0003H     ABSOLUTE     
            CODE    0026H     07DFH     UNIT         ?C?LIB_CODE
            CODE    0805H     046FH     UNIT         ?PR?PRINTF?PRINTF
            CODE    0C74H     02C4H     UNIT         ?PR?SEG_PROC?MAIN
            CODE    0F38H     00F3H     UNIT         ?PR?_MEDIAN_FILTER?FILTERING
            CODE    102BH     00DAH     UNIT         ?C_INITSEG
            CODE    1105H     00CCH     UNIT         ?PR?TIMER1_ISR?MAIN
            CODE    11D1H     00B1H     UNIT         ?PR?_MOVING_AVERAGE_FILTER?FILTERING
            CODE    1282H     008CH     UNIT         ?C_C51STARTUP
            CODE    130EH     007DH     UNIT         ?PR?_LED_DISP?LED
            CODE    138BH     0075H     UNIT         ?PR?SCHEDULER_RUN?MAIN
            CODE    1400H     0068H     UNIT         ?PR?UART1_ISR?MAIN
            CODE    1468H     0054H     UNIT         ?PR?_SET_RTC?DS1302
            CODE    14BCH     0051H     UNIT         ?PR?KEY_READ?KEY
            CODE    150DH     004FH     UNIT         ?PR?_READ_RTC?DS1302
            CODE    155CH     0046H     UNIT         ?PR?_EEPROM_WRITE?IIC
            CODE    15A2H     0044H     UNIT         ?C?LDIV
            CODE    15E6H     0042H     UNIT         ?PR?_EEPROM_READ?IIC
            CODE    1628H     003FH     UNIT         ?PR?_SEG_DISP?SEG
            CODE    1667H     003DH     UNIT         ?PR?KEY_PROC?MAIN
            CODE    16A4H     003BH     UNIT         ?PR?RD_TEMPERATURE?ONEWIRE
            CODE    16DFH     0038H     UNIT         ?PR?UT_WAVE_DATA?ULTRASOUND
            CODE    1717H     0034H     UNIT         ?PR?_READ_DS1302_BYTE?DS1302
            CODE    174BH     0030H     UNIT         ?PR?AD_DA?MAIN
            CODE    177BH     002CH     UNIT         ?PR?_I2CSENDBYTE?IIC
            CODE    17A7H     0027H     UNIT         ?PR?BEEP?LED
            CODE    17CEH     0027H     UNIT         ?PR?RELAY?LED
            CODE    17F5H     0027H     UNIT         ?PR?MOTOR?LED
            CODE    181CH     0027H     UNIT         ?PR?INIT_DS18B20?ONEWIRE
            CODE    1843H     0026H     UNIT         ?PR?GET_TEMPUTURE?MAIN
            CODE    1869H     0026H     UNIT         ?PR?MAIN?MAIN
            CODE    188FH     0025H     UNIT         ?PR?_AD_READ?IIC
            CODE    18B4H     0024H     UNIT         ?PR?I2CWAITACK?IIC
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 3


            CODE    18D8H     0023H     UNIT         ?PR?UART_PROC?MAIN
            CODE    18FBH     001EH     UNIT         ?PR?_WRITE_DS18B20?ONEWIRE
            CODE    1919H     001DH     UNIT         ?PR?I2CRECEIVEBYTE?IIC
            CODE    1936H     001DH     UNIT         ?PR?_DA_WRITE?IIC
            CODE    1953H     001DH     UNIT         ?PR?SYSTEM_INIT?INIT
            CODE    1970H     001DH     UNIT         ?PR?READ_DS18B20?ONEWIRE
            CODE    198DH     001CH     UNIT         ?PR?EEP_INIT?MAIN
            CODE    19A9H     001AH     UNIT         ?PR?_I2C_DELAY?IIC
            CODE    19C3H     001AH     UNIT         ?PR?_DELAY_ONEWIRE?ONEWIRE
            CODE    19DDH     0017H     UNIT         ?PR?UART1_INIT?UART
            CODE    19F4H     0015H     UNIT         ?PR?UT_WAVE_INIT?ULTRASOUND
            CODE    1A09H     0015H     UNIT         ?PR?TIMER1_INIT?MAIN
            CODE    1A1EH     0014H     UNIT         ?PR?_WRITE_DS1302_BYTE?DS1302
            CODE    1A32H     0013H     UNIT         ?PR?_WRITE_DS1302?DS1302
            CODE    1A45H     0013H     UNIT         ?PR?LED_OFF?LED
            CODE    1A58H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1A6BH     0011H     UNIT         ?PR?GET_DISTANCE?MAIN
            CODE    1A7CH     000FH     UNIT         ?PR?I2CSTART?IIC
            CODE    1A8BH     000FH     UNIT         ?PR?DELAY750MS?MAIN
            CODE    1A9AH     000DH     UNIT         ?CO?MAIN
            CODE    1AA7H     000CH     UNIT         ?PR?I2CSTOP?IIC
            CODE    1AB3H     000CH     UNIT         ?CO?SEG
            CODE    1ABFH     0009H     UNIT         ?PR?GET_TIME?MAIN
            CODE    1AC8H     0008H     UNIT         ?PR?_PUTCHAR?UART
            CODE    1AD0H     0007H     UNIT         ?PR?DELAY4US?ONEWIRE
            CODE    1AD7H     0007H     UNIT         ?PR?DELAY12US?ULTRASOUND
            CODE    1ADEH     0001H     UNIT         ?PR?LED_PROC?MAIN



OVERLAY MAP OF MODULE:   .\Objects\demo_micu (DS1302)


SEGMENT                                         BIT_GROUP          DATA_GROUP 
  +--> CALLED SEGMENT                        START    LENGTH     START    LENGTH
--------------------------------------------------------------------------------
?PR?TIMER1_ISR?MAIN                          -----    -----      -----    -----
  +--> ?PR?_SEG_DISP?SEG
  +--> ?PR?_LED_DISP?LED
  +--> ?PR?LED_OFF?LED

?PR?_SEG_DISP?SEG                            -----    -----      -----    -----
  +--> ?CO?SEG

*** NEW ROOT ***************************************************

?C_C51STARTUP                                -----    -----      -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                                -----    -----      -----    -----
  +--> ?PR?SYSTEM_INIT?INIT
  +--> ?PR?RD_TEMPERATURE?ONEWIRE
  +--> ?PR?DELAY750MS?MAIN
  +--> ?PR?EEP_INIT?MAIN
  +--> ?PR?UART1_INIT?UART
  +--> ?PR?_SET_RTC?DS1302
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 4


  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?SCHEDULER_INIT?MAIN
  +--> ?PR?TIMER1_INIT?MAIN
  +--> ?PR?SCHEDULER_RUN?MAIN

?PR?RD_TEMPERATURE?ONEWIRE                   -----    -----      -----    -----
  +--> ?PR?INIT_DS18B20?ONEWIRE
  +--> ?PR?_WRITE_DS18B20?ONEWIRE
  +--> ?PR?_DELAY_ONEWIRE?ONEWIRE
  +--> ?PR?READ_DS18B20?ONEWIRE

?PR?INIT_DS18B20?ONEWIRE                     0020H.0  0000H.1    -----    -----
  +--> ?PR?_DELAY_ONEWIRE?ONEWIRE

?PR?_WRITE_DS18B20?ONEWIRE                   -----    -----      -----    -----
  +--> ?PR?_DELAY_ONEWIRE?ONEWIRE

?PR?READ_DS18B20?ONEWIRE                     -----    -----      -----    -----
  +--> ?PR?DELAY4US?ONEWIRE
  +--> ?PR?_DELAY_ONEWIRE?ONEWIRE

?PR?EEP_INIT?MAIN                            -----    -----      -----    -----
  +--> ?PR?_EEPROM_WRITE?IIC
  +--> ?PR?_EEPROM_READ?IIC

?PR?_EEPROM_WRITE?IIC                        -----    -----      0022H    0005H
  +--> ?PR?I2CWAITACK?IIC
  +--> ?PR?_I2CSENDBYTE?IIC
  +--> ?PR?_I2C_DELAY?IIC
  +--> ?PR?I2CSTOP?IIC

?PR?I2CWAITACK?IIC                           -----    -----      -----    -----
  +--> ?PR?I2CSTART?IIC
  +--> ?PR?_I2CSENDBYTE?IIC
  +--> ?PR?_I2C_DELAY?IIC

?PR?I2CSTART?IIC                             -----    -----      -----    -----
  +--> ?PR?_I2C_DELAY?IIC

?PR?_I2CSENDBYTE?IIC                         -----    -----      -----    -----
  +--> ?PR?_I2C_DELAY?IIC

?PR?I2CSTOP?IIC                              -----    -----      -----    -----
  +--> ?PR?_I2C_DELAY?IIC

?PR?_EEPROM_READ?IIC                         -----    -----      0022H    0005H
  +--> ?PR?I2CWAITACK?IIC
  +--> ?PR?I2CSTART?IIC
  +--> ?PR?_I2CSENDBYTE?IIC
  +--> ?PR?I2CRECEIVEBYTE?IIC
  +--> ?PR?_I2CSENDACK?IIC
  +--> ?PR?I2CSTOP?IIC

?PR?I2CRECEIVEBYTE?IIC                       -----    -----      -----    -----
  +--> ?PR?_I2C_DELAY?IIC

?PR?_I2CSENDACK?IIC                          -----    -----      -----    -----
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 5


  +--> ?PR?_I2C_DELAY?IIC

?PR?_SET_RTC?DS1302                          -----    -----      0022H    0003H
  +--> ?PR?_WRITE_DS1302_BYTE?DS1302

?PR?_WRITE_DS1302_BYTE?DS1302                -----    -----      -----    -----
  +--> ?PR?_WRITE_DS1302?DS1302

?PR?SCHEDULER_RUN?MAIN                       -----    -----      0022H    0005H

?C_INITSEG                                   -----    -----      -----    -----
  +--> ?PR?LED_PROC?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?SEG_PROC?MAIN
  +--> ?PR?UART_PROC?MAIN
  +--> ?PR?GET_TEMPUTURE?MAIN
  +--> ?PR?GET_DISTANCE?MAIN
  +--> ?PR?GET_TIME?MAIN
  +--> ?PR?AD_DA?MAIN

?PR?KEY_PROC?MAIN                            -----    -----      -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?SEG_PROC?MAIN                            -----    -----      -----    -----
  +--> ?CO?MAIN
  +--> ?PR?PRINTF?PRINTF

?PR?PRINTF?PRINTF                            0020H.0  0001H.1    0022H    001CH
  +--> ?PR?_PUTCHAR?UART

?PR?GET_TEMPUTURE?MAIN                       -----    -----      -----    -----
  +--> ?PR?RD_TEMPERATURE?ONEWIRE
  +--> ?PR?_MEDIAN_FILTER?FILTERING

?PR?_MEDIAN_FILTER?FILTERING                 -----    -----      0022H    001AH

?PR?GET_DISTANCE?MAIN                        -----    -----      -----    -----
  +--> ?PR?UT_WAVE_DATA?ULTRASOUND
  +--> ?PR?_MOVING_AVERAGE_FILTER?FILTERING

?PR?UT_WAVE_DATA?ULTRASOUND                  -----    -----      -----    -----
  +--> ?PR?UT_WAVE_INIT?ULTRASOUND

?PR?UT_WAVE_INIT?ULTRASOUND                  -----    -----      -----    -----
  +--> ?PR?DELAY12US?ULTRASOUND

?PR?_MOVING_AVERAGE_FILTER?FILTERING         -----    -----      0022H    0001H

?PR?GET_TIME?MAIN                            -----    -----      -----    -----
  +--> ?PR?_READ_RTC?DS1302

?PR?_READ_RTC?DS1302                         -----    -----      0022H    0006H
  +--> ?PR?_READ_DS1302_BYTE?DS1302

?PR?_READ_DS1302_BYTE?DS1302                 -----    -----      -----    -----
  +--> ?PR?_WRITE_DS1302?DS1302

BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 6


?PR?AD_DA?MAIN                               -----    -----      -----    -----
  +--> ?PR?_AD_READ?IIC
  +--> ?PR?_DA_WRITE?IIC

?PR?_AD_READ?IIC                             -----    -----      -----    -----
  +--> ?PR?I2CSTART?IIC
  +--> ?PR?I2CWAITACK?IIC
  +--> ?PR?_I2CSENDBYTE?IIC
  +--> ?PR?I2CRECEIVEBYTE?IIC
  +--> ?PR?_I2CSENDACK?IIC
  +--> ?PR?I2CSTOP?IIC

?PR?_DA_WRITE?IIC                            -----    -----      -----    -----
  +--> ?PR?I2CSTART?IIC
  +--> ?PR?_I2CSENDBYTE?IIC
  +--> ?PR?I2CWAITACK?IIC



SYMBOL TABLE OF MODULE:  .\Objects\demo_micu (DS1302)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        DS1302
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  B:00A8H.7       PUBLIC        EA
  D:00F8H         PUBLIC        P7
  C:150DH         PUBLIC        _Read_Rtc
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:00D8H         PUBLIC        CCON
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1468H         PUBLIC        _Set_Rtc
  C:1717H         PUBLIC        _Read_Ds1302_Byte
  B:00A0H.3       PUBLIC        SDA
  B:0090H.7       PUBLIC        SCK
  C:1A1EH         PUBLIC        _Write_Ds1302_Byte
  C:1A32H         PUBLIC        _Write_Ds1302
  B:0090H.3       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  -------         PROC          _WRITE_DS1302
  D:0007H         SYMBOL        temp
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1A32H         LINE#         14
  C:1A32H         LINE#         15
  C:1A32H         LINE#         17
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 7


  C:1A34H         LINE#         18
  C:1A34H         LINE#         19
  C:1A36H         LINE#         20
  C:1A3AH         LINE#         21
  C:1A3EH         LINE#         22
  C:1A40H         LINE#         23
  C:1A44H         LINE#         24
  -------         ENDPROC       _WRITE_DS1302
  -------         PROC          _WRITE_DS1302_BYTE
  D:0007H         SYMBOL        address
  D:0005H         SYMBOL        dat
  C:1A1EH         LINE#         27
  C:1A1EH         LINE#         28
  C:1A1EH         LINE#         29
  C:1A20H         LINE#         30
  C:1A21H         LINE#         31
  C:1A23H         LINE#         32
  C:1A24H         LINE#         33
  C:1A26H         LINE#         34
  C:1A27H         LINE#         35
  C:1A2AH         LINE#         36
  C:1A2FH         LINE#         37
  C:1A31H         LINE#         38
  -------         ENDPROC       _WRITE_DS1302_BYTE
  -------         PROC          _READ_DS1302_BYTE
  D:0007H         SYMBOL        address
  -------         DO            
  D:0007H         SYMBOL        i
  D:0005H         SYMBOL        temp
  -------         ENDDO         
  C:1717H         LINE#         41
  C:1717H         LINE#         42
  C:1717H         LINE#         43
  C:1719H         LINE#         44
  C:171BH         LINE#         45
  C:171CH         LINE#         46
  C:171EH         LINE#         47
  C:171FH         LINE#         48
  C:1721H         LINE#         49
  C:1722H         LINE#         50
  C:1725H         LINE#         51
  C:1727H         LINE#         52
  C:1727H         LINE#         53
  C:1729H         LINE#         54
  C:172DH         LINE#         55
  C:1730H         LINE#         56
  C:1733H         LINE#         57
  C:1735H         LINE#         58
  C:1739H         LINE#         59
  C:173BH         LINE#         60
  C:173CH         LINE#         61
  C:173EH         LINE#         62
  C:173FH         LINE#         63
  C:1741H         LINE#         64
  C:1742H         LINE#         65
  C:1744H         LINE#         66
  C:1745H         LINE#         67
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 8


  C:1747H         LINE#         68
  C:1748H         LINE#         69
  C:174AH         LINE#         70
  -------         ENDPROC       _READ_DS1302_BYTE
  -------         PROC          _SET_RTC
  D:0022H         SYMBOL        ucRtc
  -------         DO            
  D:0004H         SYMBOL        i
  -------         ENDDO         
  C:1468H         LINE#         71
  C:146EH         LINE#         72
  C:146EH         LINE#         74
  C:1475H         LINE#         75
  C:147CH         LINE#         76
  C:147EH         LINE#         77
  C:14B5H         LINE#         78
  -------         ENDPROC       _SET_RTC
  -------         PROC          _READ_RTC
  D:0022H         SYMBOL        ucRtc
  -------         DO            
  D:0025H         SYMBOL        i
  D:0026H         SYMBOL        temp
  -------         ENDDO         
  C:150DH         LINE#         81
  C:1513H         LINE#         82
  C:1513H         LINE#         85
  C:1515H         LINE#         86
  C:1518H         LINE#         87
  C:1518H         LINE#         88
  C:152AH         LINE#         89
  C:1550H         LINE#         90
  C:1559H         LINE#         91
  C:155BH         LINE#         92
  -------         ENDPROC       _READ_RTC
  -------         ENDMOD        DS1302

  -------         MODULE        FILTERING
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:0F38H         PUBLIC        _Median_Filter
  X:0000H         PUBLIC        mean_index
  X:0001H         PUBLIC        mean_data_array
  X:0006H         PUBLIC        mean_data_count
  X:0007H         PUBLIC        mean_sum
  C:11D1H         PUBLIC        _Moving_Average_Filter
  X:000BH         PUBLIC        median_index
  X:000CH         PUBLIC        median_data_array
  X:0020H         PUBLIC        median_data_count
  -------         PROC          _MEDIAN_FILTER
  D:0004H         SYMBOL        new_data
  -------         DO            
  D:0022H         SYMBOL        sorted_data
  D:0036H         SYMBOL        temp
  D:003AH         SYMBOL        i
  D:003BH         SYMBOL        j
  -------         ENDDO         
  C:0F38H         LINE#         11
  C:0F38H         LINE#         12
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 9


  C:0F38H         LINE#         17
  C:0F46H         LINE#         18
  C:0F58H         LINE#         19
  C:0F60H         LINE#         20
  C:0F63H         LINE#         23
  C:0F70H         LINE#         24
  C:0F70H         LINE#         25
  C:0F90H         LINE#         26
  C:0F94H         LINE#         29
  C:0FA2H         LINE#         30
  C:0FA2H         LINE#         31
  C:0FB3H         LINE#         32
  C:0FB3H         LINE#         33
  C:0FD2H         LINE#         34
  C:0FD2H         LINE#         36
  C:0FE6H         LINE#         37
  C:0FFEH         LINE#         38
  C:1012H         LINE#         39
  C:1012H         LINE#         40
  C:1016H         LINE#         41
  C:101BH         LINE#         42
  -------         ENDPROC       _MEDIAN_FILTER
  -------         PROC          _MOVING_AVERAGE_FILTER
  D:0022H         SYMBOL        new_data
  C:11D1H         LINE#         53
  C:11D3H         LINE#         54
  C:11D3H         LINE#         55
  C:120CH         LINE#         56
  C:1215H         LINE#         57
  C:1241H         LINE#         58
  C:1255H         LINE#         59
  C:125DH         LINE#         60
  C:1260H         LINE#         61
  -------         ENDPROC       _MOVING_AVERAGE_FILTER
  -------         ENDMOD        FILTERING

  -------         MODULE        IIC
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:188FH         PUBLIC        _Ad_Read
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  D:00F8H         PUBLIC        P7
  D:00A8H         PUBLIC        IE
  C:19ADH         SYMBOL        _I2C_Delay
  C:1936H         PUBLIC        _Da_Write
  D:00B8H         PUBLIC        IP
  C:1919H         PUBLIC        I2CReceiveByte
  D:00D8H         PUBLIC        CCON
  C:15E6H         PUBLIC        _EEPROM_Read
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:00A0H.1       PUBLIC        sda
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 10


  C:155CH         PUBLIC        _EEPROM_Write
  B:00A0H.0       PUBLIC        scl
  C:1A7CH         PUBLIC        I2CStart
  C:0003H         PUBLIC        _I2CSendAck
  C:177BH         PUBLIC        _I2CSendByte
  C:18C6H         PUBLIC        I2CWaitAck
  C:1AA7H         PUBLIC        I2CStop
  D:00D0H         PUBLIC        PSW
  C:19A9H         SYMBOL        L?0036
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  C:19A9H         SYMBOL        L?0036
  -------         PROC          _I2C_DELAY
  D:0007H         SYMBOL        n
  C:19ADH         LINE#         14
  C:19ADH         LINE#         15
  C:19ADH         LINE#         16
  C:19AEH         LINE#         17
  C:19AFH         LINE#         18
  C:19B0H         LINE#         19
  C:19B1H         LINE#         20
  C:19B2H         LINE#         21
  C:19B3H         LINE#         22
  C:19B4H         LINE#         23
  C:19B5H         LINE#         24
  C:19B6H         LINE#         25
  C:19B7H         LINE#         26
  C:19B8H         LINE#         27
  C:19B9H         LINE#         28
  C:19BAH         LINE#         29
  C:19BBH         LINE#         30
  C:19BCH         LINE#         31
  C:19C2H         LINE#         32
  -------         ENDPROC       _I2C_DELAY
  -------         PROC          I2CSTART
  C:1A7CH         LINE#         35
  C:1A7CH         LINE#         36
  C:1A7EH         LINE#         37
  C:1A7EH         LINE#         38
  C:1A81H         LINE#         39
  C:1A83H         LINE#         40
  C:1A88H         LINE#         41
  C:1A8AH         LINE#         42
  -------         ENDPROC       I2CSTART
  -------         PROC          I2CSTOP
  C:1AA7H         LINE#         45
  C:1AA7H         LINE#         46
  C:1AA9H         LINE#         47
  C:1AA9H         LINE#         48
  C:1AACH         LINE#         49
  C:1AAEH         LINE#         50
  -------         ENDPROC       I2CSTOP
  -------         PROC          _I2CSENDBYTE
  D:0005H         SYMBOL        byt
  -------         DO            
  D:0004H         SYMBOL        i
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 11


  C:177BH         LINE#         54
  C:177DH         LINE#         57
  C:177FH         LINE#         58
  C:1781H         LINE#         59
  C:1786H         LINE#         60
  C:178AH         LINE#         61
  C:178CH         LINE#         62
  C:178EH         LINE#         63
  C:1790H         LINE#         64
  C:1790H         LINE#         65
  C:1795H         LINE#         66
  C:1797H         LINE#         67
  C:179BH         LINE#         68
  C:17A0H         LINE#         69
  C:17A4H         LINE#         71
  C:17A6H         LINE#         72
  -------         ENDPROC       _I2CSENDBYTE
  -------         PROC          I2CRECEIVEBYTE
  -------         DO            
  D:0005H         SYMBOL        da
  D:0004H         SYMBOL        i
  -------         ENDDO         
  C:1919H         LINE#         75
  C:1919H         LINE#         78
  C:191BH         LINE#         79
  C:191BH         LINE#         80
  C:191EH         LINE#         81
  C:1922H         LINE#         82
  C:1928H         LINE#         83
  C:192AH         LINE#         84
  C:192FH         LINE#         85
  C:1933H         LINE#         86
  C:1935H         LINE#         87
  -------         ENDPROC       I2CRECEIVEBYTE
  C:18BBH         SYMBOL        L?0034
  -------         PROC          L?0033
  -------         ENDPROC       L?0033
  C:18BBH         SYMBOL        L?0034
  -------         PROC          I2CWAITACK
  -------         DO            
  D:0005H         SYMBOL        ackbit
  -------         ENDDO         
  C:18C6H         LINE#         90
  C:18C6H         LINE#         93
  C:18C6H         LINE#         94
  C:18C9H         LINE#         95
  C:18CEH         LINE#         96
  C:18D0H         LINE#         97
  C:18D5H         LINE#         99
  C:18D7H         LINE#         100
  -------         ENDPROC       I2CWAITACK
  -------         PROC          _I2CSENDACK
  D:0007H         SYMBOL        ackbit
  C:0003H         LINE#         103
  C:0003H         LINE#         104
  C:0005H         LINE#         105
  C:000AH         LINE#         106
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 12


  C:000FH         LINE#         107
  C:000FH         LINE#         108
  C:0012H         LINE#         109
  C:0014H         LINE#         110
  C:0016H         LINE#         111
  -------         ENDPROC       _I2CSENDACK
  -------         PROC          _AD_READ
  D:0003H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        temp
  -------         ENDDO         
  C:188FH         LINE#         114
  C:1891H         LINE#         117
  C:1894H         LINE#         118
  C:1896H         LINE#         119
  C:1896H         LINE#         120
  C:1896H         LINE#         121
  C:1899H         LINE#         123
  C:189CH         LINE#         124
  C:18A1H         LINE#         125
  C:18A4H         LINE#         126
  C:18A9H         LINE#         127
  C:18AEH         LINE#         128
  C:18B1H         LINE#         129
  C:18B3H         LINE#         130
  -------         ENDPROC       _AD_READ
  -------         PROC          _DA_WRITE
  D:0003H         SYMBOL        dat
  C:1936H         LINE#         132
  C:1938H         LINE#         134
  C:193BH         LINE#         135
  C:1940H         LINE#         136
  C:1943H         LINE#         137
  C:1948H         LINE#         138
  C:194BH         LINE#         139
  C:1950H         LINE#         140
  -------         ENDPROC       _DA_WRITE
  -------         PROC          _EEPROM_WRITE
  D:0022H         SYMBOL        str
  D:0003H         SYMBOL        addr
  D:0026H         SYMBOL        num
  C:155CH         LINE#         142
  C:1562H         LINE#         143
  C:1562H         LINE#         144
  C:1562H         LINE#         145
  C:1562H         LINE#         146
  C:1562H         LINE#         147
  C:1565H         LINE#         149
  C:156CH         LINE#         150
  C:1581H         LINE#         151
  C:1584H         LINE#         152
  C:1589H         LINE#         153
  C:158BH         LINE#         154
  C:158EH         LINE#         155
  C:1593H         LINE#         156
  C:1598H         LINE#         157
  C:159DH         LINE#         158
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 13


  -------         ENDPROC       _EEPROM_WRITE
  -------         PROC          _EEPROM_READ
  D:0022H         SYMBOL        str
  D:0003H         SYMBOL        addr
  D:0026H         SYMBOL        num
  C:15E6H         LINE#         160
  C:15ECH         LINE#         161
  C:15ECH         LINE#         162
  C:15ECH         LINE#         163
  C:15ECH         LINE#         164
  C:15ECH         LINE#         165
  C:15EFH         LINE#         167
  C:15F2H         LINE#         168
  C:15F7H         LINE#         169
  C:15FAH         LINE#         170
  C:1601H         LINE#         171
  C:1616H         LINE#         172
  C:161AH         LINE#         173
  C:161EH         LINE#         175
  C:1623H         LINE#         176
  C:1625H         LINE#         177
  -------         ENDPROC       _EEPROM_READ
  -------         ENDMOD        IIC

  -------         MODULE        INIT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:1953H         PUBLIC        System_Init
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  D:00F8H         PUBLIC        P7
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:00D8H         PUBLIC        CCON
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00D0H         PUBLIC        PSW
  -------         PROC          SYSTEM_INIT
  C:1953H         LINE#         2
  C:1953H         LINE#         3
  C:1956H         LINE#         4
  C:195EH         LINE#         5
  C:1961H         LINE#         7
  C:1964H         LINE#         8
  C:196CH         LINE#         9
  C:196FH         LINE#         10
  -------         ENDPROC       SYSTEM_INIT
  -------         ENDMOD        INIT

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 14


  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  D:00F8H         PUBLIC        P7
  C:14BCH         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:00D8H         PUBLIC        CCON
  B:00B0H.0       PUBLIC        P30
  B:00B0H.1       PUBLIC        P31
  D:0098H         PUBLIC        SCON
  B:00B0H.2       PUBLIC        P32
  D:0088H         PUBLIC        TCON
  B:00C0H.2       PUBLIC        P42
  B:00B0H.3       PUBLIC        P33
  B:00C0H.4       PUBLIC        P44
  B:00B0H.5       PUBLIC        P35
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:14BCH         LINE#         2
  C:14BCH         LINE#         3
  C:14BEH         LINE#         6
  C:14C0H         LINE#         7
  C:14C2H         LINE#         8
  C:14C4H         LINE#         10
  C:14C9H         LINE#         11
  C:14CEH         LINE#         12
  C:14D3H         LINE#         13
  C:14D8H         LINE#         15
  C:14DAH         LINE#         16
  C:14DCH         LINE#         17
  C:14DEH         LINE#         19
  C:14E3H         LINE#         20
  C:14E8H         LINE#         21
  C:14EDH         LINE#         22
  C:14F2H         LINE#         24
  C:14F4H         LINE#         25
  C:14F6H         LINE#         26
  C:14F8H         LINE#         28
  C:14FDH         LINE#         29
  C:1502H         LINE#         30
  C:1507H         LINE#         31
  C:150CH         LINE#         44
  C:150CH         LINE#         45
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        LED
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:17CEH         PUBLIC        Relay
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 15


  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  D:00F8H         PUBLIC        P7
  C:130EH         PUBLIC        _Led_Disp
  D:00A8H         PUBLIC        IE
  C:1A45H         PUBLIC        Led_Off
  C:17F5H         PUBLIC        MOTOR
  D:00B8H         PUBLIC        IP
  D:00D8H         PUBLIC        CCON
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  I:0008H         PUBLIC        temp_0
  I:0009H         PUBLIC        temp_1
  I:000AH         PUBLIC        temp_old_0
  I:000BH         PUBLIC        temp_old_1
  D:00D0H         PUBLIC        PSW
  C:17A7H         PUBLIC        Beep
  -------         PROC          _LED_DISP
  D:0001H         SYMBOL        ucLed
  C:130EH         LINE#         9
  C:130EH         LINE#         10
  C:130EH         LINE#         12
  C:1312H         LINE#         13
  C:136EH         LINE#         15
  C:1374H         LINE#         16
  C:1374H         LINE#         17
  C:137BH         LINE#         18
  C:1383H         LINE#         19
  C:1386H         LINE#         20
  C:138AH         LINE#         21
  C:138AH         LINE#         22
  -------         ENDPROC       _LED_DISP
  -------         PROC          LED_OFF
  C:1A45H         LINE#         24
  C:1A45H         LINE#         25
  C:1A45H         LINE#         27
  C:1A48H         LINE#         28
  C:1A50H         LINE#         29
  C:1A53H         LINE#         30
  C:1A57H         LINE#         31
  -------         ENDPROC       LED_OFF
  -------         PROC          BEEP
  B:0021H.1       SYMBOL        enable
  C:17A7H         LINE#         37
  C:17A7H         LINE#         38
  C:17A7H         LINE#         39
  C:17ADH         LINE#         40
  C:17B2H         LINE#         42
  C:17B5H         LINE#         43
  C:17BEH         LINE#         44
  C:17BEH         LINE#         45
  C:17C0H         LINE#         46
  C:17C8H         LINE#         47
  C:17CBH         LINE#         48
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 16


  C:17CDH         LINE#         49
  C:17CDH         LINE#         50
  -------         ENDPROC       BEEP
  -------         PROC          RELAY
  B:0021H.2       SYMBOL        enable
  C:17CEH         LINE#         53
  C:17CEH         LINE#         54
  C:17CEH         LINE#         55
  C:17D4H         LINE#         56
  C:17D9H         LINE#         58
  C:17DCH         LINE#         59
  C:17E5H         LINE#         60
  C:17E5H         LINE#         61
  C:17E7H         LINE#         62
  C:17EFH         LINE#         63
  C:17F2H         LINE#         64
  C:17F4H         LINE#         65
  C:17F4H         LINE#         66
  -------         ENDPROC       RELAY
  -------         PROC          MOTOR
  B:0021H.3       SYMBOL        enable
  C:17F5H         LINE#         69
  C:17F5H         LINE#         70
  C:17F5H         LINE#         71
  C:17FBH         LINE#         72
  C:1800H         LINE#         74
  C:1803H         LINE#         75
  C:180CH         LINE#         76
  C:180CH         LINE#         77
  C:180EH         LINE#         78
  C:1816H         LINE#         79
  C:1819H         LINE#         80
  C:181BH         LINE#         81
  C:181BH         LINE#         82
  -------         ENDPROC       MOTOR
  -------         ENDMOD        LED

  -------         MODULE        ONEWIRE
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:1970H         PUBLIC        Read_DS18B20
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  D:00F8H         PUBLIC        P7
  D:00A8H         PUBLIC        IE
  C:19C7H         PUBLIC        _Delay_OneWire
  C:1AD0H         PUBLIC        Delay4us
  C:18FBH         PUBLIC        _Write_DS18B20
  B:0090H.4       PUBLIC        DQ
  D:00B8H         PUBLIC        IP
  C:181CH         PUBLIC        init_ds18b20
  D:00D8H         PUBLIC        CCON
  D:0098H         PUBLIC        SCON
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 17


  D:0088H         PUBLIC        TCON
  C:16A4H         PUBLIC        rd_temperature
  D:00D0H         PUBLIC        PSW
  -------         PROC          DELAY4US
  -------         DO            
  D:0007H         SYMBOL        i
  -------         ENDDO         
  C:1AD0H         LINE#         12
  C:1AD0H         LINE#         13
  C:1AD0H         LINE#         16
  C:1AD1H         LINE#         17
  C:1AD2H         LINE#         18
  C:1AD4H         LINE#         19
  C:1AD6H         LINE#         20
  -------         ENDPROC       DELAY4US
  C:19C3H         SYMBOL        L?0023
  -------         PROC          L?0022
  -------         ENDPROC       L?0022
  C:19C3H         SYMBOL        L?0023
  -------         PROC          _DELAY_ONEWIRE
  D:0006H         SYMBOL        t
  -------         DO            
  D:0005H         SYMBOL        i
  -------         ENDDO         
  C:19C7H         LINE#         23
  C:19C7H         LINE#         25
  C:19D1H         LINE#         26
  C:19DCH         LINE#         27
  C:19DCH         LINE#         28
  -------         ENDPROC       _DELAY_ONEWIRE
  -------         PROC          _WRITE_DS18B20
  D:0001H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  -------         ENDDO         
  C:18FBH         LINE#         31
  C:18FDH         LINE#         33
  C:18FFH         LINE#         34
  C:1901H         LINE#         35
  C:1905H         LINE#         36
  C:1908H         LINE#         37
  C:190AH         LINE#         38
  C:190EH         LINE#         39
  C:1912H         LINE#         40
  -------         ENDPROC       _WRITE_DS18B20
  -------         PROC          READ_DS18B20
  -------         DO            
  D:0003H         SYMBOL        i
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:1970H         LINE#         44
  C:1970H         LINE#         48
  C:1972H         LINE#         49
  C:1974H         LINE#         50
  C:1978H         LINE#         51
  C:197AH         LINE#         52
  C:197DH         LINE#         53
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 18


  C:1980H         LINE#         54
  C:1983H         LINE#         55
  C:1983H         LINE#         56
  C:1986H         LINE#         57
  C:198AH         LINE#         58
  C:198CH         LINE#         59
  -------         ENDPROC       READ_DS18B20
  -------         PROC          INIT_DS18B20
  -------         DO            
  B:0020H.0       SYMBOL        initflag
  -------         ENDDO         
  C:181CH         LINE#         62
  C:181CH         LINE#         63
  C:181EH         LINE#         65
  C:1820H         LINE#         66
  C:1827H         LINE#         67
  C:1829H         LINE#         68
  C:1830H         LINE#         69
  C:1832H         LINE#         70
  C:1839H         LINE#         71
  C:183DH         LINE#         72
  C:1840H         LINE#         74
  C:1842H         LINE#         75
  -------         ENDPROC       INIT_DS18B20
  -------         PROC          RD_TEMPERATURE
  -------         DO            
  D:0001H         SYMBOL        low
  D:0007H         SYMBOL        high
  -------         ENDDO         
  C:16A4H         LINE#         77
  C:16A4H         LINE#         79
  C:16A7H         LINE#         80
  C:16ACH         LINE#         81
  C:16B1H         LINE#         82
  C:16B8H         LINE#         83
  C:16BBH         LINE#         84
  C:16C0H         LINE#         85
  C:16C5H         LINE#         86
  C:16CAH         LINE#         87
  C:16CDH         LINE#         88
  -------         ENDPROC       RD_TEMPERATURE
  -------         ENDMOD        ONEWIRE

  -------         MODULE        SEG
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  D:00F8H         PUBLIC        P7
  D:00A8H         PUBLIC        IE
  C:1628H         PUBLIC        _Seg_Disp
  D:00B8H         PUBLIC        IP
  D:00D8H         PUBLIC        CCON
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 19


  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1AB3H         PUBLIC        seg_dula
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SEG_DISP
  D:0007H         SYMBOL        wela
  D:0005H         SYMBOL        dula
  D:0003H         SYMBOL        point
  C:1628H         LINE#         6
  C:1628H         LINE#         8
  C:162BH         LINE#         9
  C:1633H         LINE#         10
  C:1636H         LINE#         13
  C:1643H         LINE#         14
  C:164BH         LINE#         15
  C:164EH         LINE#         18
  C:1655H         LINE#         19
  C:165BH         LINE#         20
  C:1663H         LINE#         21
  C:1666H         LINE#         22
  -------         ENDPROC       _SEG_DISP
  -------         ENDMOD        SEG

  -------         MODULE        UART
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  B:00A8H.7       PUBLIC        EA
  D:00F8H         PUBLIC        P7
  D:00A8H         PUBLIC        IE
  B:00A8H.4       PUBLIC        ES
  D:00B8H         PUBLIC        IP
  B:0098H.1       PUBLIC        TI
  D:00D8H         PUBLIC        CCON
  D:0099H         PUBLIC        SBUF
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:008EH         PUBLIC        AUXR
  C:19DDH         PUBLIC        Uart1_Init
  D:00D6H         PUBLIC        T2H
  D:00D7H         PUBLIC        T2L
  D:00D0H         PUBLIC        PSW
  C:1AC8H         PUBLIC        _putchar
  -------         PROC          UART1_INIT
  C:19DDH         LINE#         2
  C:19DDH         LINE#         3
  C:19DDH         LINE#         4
  C:19E0H         LINE#         5
  C:19E3H         LINE#         6
  C:19E6H         LINE#         7
  C:19E9H         LINE#         8
  C:19ECH         LINE#         9
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 20


  C:19EFH         LINE#         10
  C:19F1H         LINE#         11
  C:19F3H         LINE#         12
  -------         ENDPROC       UART1_INIT
  -------         PROC          _PUTCHAR
  D:0007H         SYMBOL        ch
  C:1AC8H         LINE#         14
  C:1AC8H         LINE#         15
  C:1AC8H         LINE#         16
  C:1ACAH         LINE#         17
  C:1ACDH         LINE#         19
  C:1ACFH         LINE#         20
  C:1ACFH         LINE#         21
  -------         ENDPROC       _PUTCHAR
  -------         ENDMOD        UART

  -------         MODULE        ULTRASOUND
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  B:00A8H.7       PUBLIC        EA
  D:00F8H         PUBLIC        P7
  B:00D8H.7       PUBLIC        CF
  D:00F9H         PUBLIC        CH
  D:00A8H         PUBLIC        IE
  D:00E9H         PUBLIC        CL
  B:00D8H.6       PUBLIC        CR
  D:00B8H         PUBLIC        IP
  C:16DFH         PUBLIC        Ut_Wave_Data
  D:00D9H         PUBLIC        CMOD
  D:00D8H         PUBLIC        CCON
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:19F4H         PUBLIC        Ut_Wave_Init
  C:1AD7H         PUBLIC        Delay12us
  B:0090H.1       PUBLIC        Rx
  B:0090H.0       PUBLIC        Tx
  D:00D0H         PUBLIC        PSW
  -------         PROC          DELAY12US
  -------         DO            
  D:0007H         SYMBOL        i
  -------         ENDDO         
  C:1AD7H         LINE#         7
  C:1AD7H         LINE#         8
  C:1AD7H         LINE#         11
  C:1AD8H         LINE#         12
  C:1AD9H         LINE#         13
  C:1ADBH         LINE#         14
  C:1ADDH         LINE#         16
  -------         ENDPROC       DELAY12US
  -------         PROC          UT_WAVE_INIT
  -------         DO            
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 21


  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:19F4H         LINE#         18
  C:19F4H         LINE#         19
  C:19F4H         LINE#         21
  C:19F6H         LINE#         22
  C:19F8H         LINE#         23
  C:19F8H         LINE#         24
  C:19FAH         LINE#         25
  C:19FDH         LINE#         26
  C:19FFH         LINE#         27
  C:1A02H         LINE#         28
  C:1A06H         LINE#         29
  C:1A08H         LINE#         30
  -------         ENDPROC       UT_WAVE_INIT
  -------         PROC          UT_WAVE_DATA
  -------         DO            
  D:0006H         SYMBOL        time
  -------         ENDDO         
  C:16DFH         LINE#         32
  C:16DFH         LINE#         33
  C:16DFH         LINE#         35
  C:16E2H         LINE#         36
  C:16E6H         LINE#         37
  C:16E9H         LINE#         38
  C:16EBH         LINE#         39
  C:16F1H         LINE#         40
  C:16F3H         LINE#         41
  C:16F6H         LINE#         42
  C:16F6H         LINE#         43
  C:16FEH         LINE#         44
  C:1712H         LINE#         45
  C:1712H         LINE#         47
  C:1712H         LINE#         48
  C:1714H         LINE#         49
  C:1716H         LINE#         50
  C:1716H         LINE#         51
  -------         ENDPROC       UT_WAVE_DATA
  -------         ENDMOD        ULTRASOUND

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  I:003EH         PUBLIC        Uart_Recv_Tick
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  I:003FH         PUBLIC        ucRtc
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00E8H         PUBLIC        P6
  B:00A8H.7       PUBLIC        EA
  D:00F8H         PUBLIC        P7
  I:0042H         PUBLIC        Eep_Write
  I:004AH         PUBLIC        Seg_Show_Mode
  C:1ADEH         PUBLIC        Led_Proc
  D:00A8H         PUBLIC        IE
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 22


  I:004BH         PUBLIC        Freq
  C:198DH         PUBLIC        Eep_Init
  C:18D8H         PUBLIC        Uart_Proc
  C:0C74H         PUBLIC        Seg_Proc
  D:00B8H         PUBLIC        IP
  I:004DH         PUBLIC        Scheduler_Task
  B:0098H.0       PUBLIC        RI
  C:001EH         PUBLIC        Scheduler_Init
  X:0021H         PUBLIC        Uart_Buf
  X:002BH         PUBLIC        Seg_Buf
  C:1667H         PUBLIC        Key_Proc
  I:00A5H         PUBLIC        pwm_compare
  D:00D8H         PUBLIC        CCON
  I:00A6H         PUBLIC        Uart_Rx_Index
  I:00A7H         PUBLIC        Key_Down
  I:00A8H         PUBLIC        Uart_Rx_Flag
  C:1869H         PUBLIC        main
  I:00A9H         PUBLIC        AD_in_100x
  I:00ABH         PUBLIC        DA_out_100x
  C:1A58H         PUBLIC        Timer0_Init
  I:00ADH         PUBLIC        Key_Old
  C:1A09H         PUBLIC        Timer1_Init
  I:00AEH         PUBLIC        Key_Val
  D:0099H         PUBLIC        SBUF
  C:1ABFH         PUBLIC        Get_time
  I:00AFH         PUBLIC        Seg_Pos
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  C:138BH         PUBLIC        Scheduler_Run
  I:00B0H         PUBLIC        pwm_period
  D:008EH         PUBLIC        AUXR
  C:1105H         PUBLIC        Timer1_Isr
  B:00A8H.3       PUBLIC        ET1
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  D:008CH         PUBLIC        TH0
  D:008DH         PUBLIC        TH1
  I:00B1H         PUBLIC        distance
  I:00B3H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  C:1A6BH         PUBLIC        Get_distance
  I:00B4H         PUBLIC        time1000ms
  B:0088H.4       PUBLIC        TR0
  B:0088H.6       PUBLIC        TR1
  I:00B6H         PUBLIC        temputure_100x
  C:1400H         PUBLIC        Uart1_Isr
  I:00B8H         PUBLIC        task_num
  C:174BH         PUBLIC        AD_DA
  X:0033H         PUBLIC        ucLed
  C:1A8BH         PUBLIC        Delay750ms
  C:1843H         PUBLIC        Get_temputure
  I:00B9H         PUBLIC        Eep_Read
  I:00C1H         PUBLIC        uwTick
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_PROC
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 23


  C:1667H         LINE#         51
  C:1667H         LINE#         52
  C:1667H         LINE#         53
  C:166EH         LINE#         54
  C:1678H         LINE#         55
  C:1681H         LINE#         56
  C:1685H         LINE#         58
  C:1688H         LINE#         59
  C:1692H         LINE#         61
  C:1699H         LINE#         62
  C:169CH         LINE#         63
  C:16A0H         LINE#         64
  C:16A3H         LINE#         65
  -------         ENDPROC       KEY_PROC
  -------         PROC          SEG_PROC
  C:0C74H         LINE#         68
  C:0C74H         LINE#         69
  C:0C74H         LINE#         71
  C:0C99H         LINE#         72
  C:0C99H         LINE#         74
  C:0C99H         LINE#         75
  C:0CA4H         LINE#         76
  C:0CADH         LINE#         77
  C:0CB1H         LINE#         78
  C:0CBCH         LINE#         79
  C:0CC5H         LINE#         80
  C:0CC9H         LINE#         81
  C:0CD4H         LINE#         82
  C:0CDDH         LINE#         83
  C:0CE0H         LINE#         85
  C:0CE0H         LINE#         86
  C:0CFCH         LINE#         87
  C:0D14H         LINE#         88
  C:0D30H         LINE#         89
  C:0D3FH         LINE#         90
  C:0D42H         LINE#         91
  C:0D42H         LINE#         92
  C:0D42H         LINE#         93
  C:0D42H         LINE#         94
  C:0D45H         LINE#         96
  C:0D45H         LINE#         97
  C:0D61H         LINE#         98
  C:0D77H         LINE#         99
  C:0D89H         LINE#         100
  C:0D8DH         LINE#         101
  C:0D8EH         LINE#         102
  C:0D8EH         LINE#         103
  C:0D8EH         LINE#         104
  C:0D8EH         LINE#         105
  C:0D90H         LINE#         107
  C:0D90H         LINE#         108
  C:0DAEH         LINE#         109
  C:0DC4H         LINE#         110
  C:0DD6H         LINE#         111
  C:0DDAH         LINE#         112
  C:0DDCH         LINE#         113
  C:0DDEH         LINE#         114
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 24


  C:0DE0H         LINE#         115
  C:0DE2H         LINE#         116
  C:0DE5H         LINE#         118
  C:0DE5H         LINE#         119
  C:0E06H         LINE#         120
  C:0E27H         LINE#         121
  C:0E48H         LINE#         122
  C:0E64H         LINE#         123
  C:0E7AH         LINE#         124
  C:0E96H         LINE#         125
  C:0EACH         LINE#         126
  C:0EBEH         LINE#         127
  C:0EC0H         LINE#         128
  C:0EC0H         LINE#         129
  C:0EC6H         LINE#         130
  C:0ECCH         LINE#         131
  C:0ED2H         LINE#         132
  C:0ED8H         LINE#         133
  C:0EDEH         LINE#         134
  C:0EE4H         LINE#         135
  C:0EEAH         LINE#         136
  C:0EF0H         LINE#         138
  C:0EF0H         LINE#         139
  C:0EF0H         LINE#         141
  -------         ENDPROC       SEG_PROC
  -------         PROC          UART_PROC
  C:18D8H         LINE#         145
  C:18D8H         LINE#         146
  C:18D8H         LINE#         147
  C:18DEH         LINE#         149
  C:18E6H         LINE#         150
  C:18E6H         LINE#         151
  C:18E8H         LINE#         152
  C:18EBH         LINE#         153
  C:18F6H         LINE#         154
  C:18FAH         LINE#         155
  C:18FAH         LINE#         156
  -------         ENDPROC       UART_PROC
  -------         PROC          LED_PROC
  C:1ADEH         LINE#         159
  C:1ADEH         LINE#         160
  C:1ADEH         LINE#         163
  -------         ENDPROC       LED_PROC
  -------         PROC          GET_TIME
  C:1ABFH         LINE#         165
  C:1ABFH         LINE#         166
  C:1ABFH         LINE#         167
  -------         ENDPROC       GET_TIME
  -------         PROC          GET_TEMPUTURE
  -------         DO            
  D:0006H         SYMBOL        temp
  -------         ENDDO         
  C:1843H         LINE#         171
  C:1843H         LINE#         172
  C:1843H         LINE#         173
  C:1853H         LINE#         174
  C:1868H         LINE#         175
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 25


  -------         ENDPROC       GET_TEMPUTURE
  -------         PROC          GET_DISTANCE
  -------         DO            
  D:0006H         SYMBOL        temp
  -------         ENDDO         
  C:1A6BH         LINE#         178
  C:1A6BH         LINE#         179
  C:1A6BH         LINE#         180
  C:1A6EH         LINE#         181
  C:1A7BH         LINE#         182
  -------         ENDPROC       GET_DISTANCE
  -------         PROC          AD_DA
  C:174BH         LINE#         185
  C:174BH         LINE#         186
  C:174BH         LINE#         187
  C:1766H         LINE#         188
  -------         ENDPROC       AD_DA
  -------         PROC          TIMER0_INIT
  C:1A58H         LINE#         192
  C:1A58H         LINE#         193
  C:1A58H         LINE#         194
  C:1A5BH         LINE#         195
  C:1A5EH         LINE#         196
  C:1A61H         LINE#         197
  C:1A64H         LINE#         198
  C:1A66H         LINE#         199
  C:1A68H         LINE#         200
  C:1A6AH         LINE#         201
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER1_INIT
  C:1A09H         LINE#         203
  C:1A09H         LINE#         204
  C:1A09H         LINE#         205
  C:1A0CH         LINE#         206
  C:1A0FH         LINE#         207
  C:1A12H         LINE#         208
  C:1A15H         LINE#         209
  C:1A17H         LINE#         210
  C:1A19H         LINE#         211
  C:1A1BH         LINE#         212
  C:1A1DH         LINE#         213
  -------         ENDPROC       TIMER1_INIT
  -------         PROC          TIMER1_ISR
  C:1105H         LINE#         216
  C:1122H         LINE#         218
  C:1139H         LINE#         219
  C:113EH         LINE#         220
  C:1141H         LINE#         221
  C:1148H         LINE#         223
  C:115AH         LINE#         224
  C:115AH         LINE#         225
  C:1161H         LINE#         226
  C:116CH         LINE#         227
  C:1172H         LINE#         228
  C:1172H         LINE#         231
  C:1180H         LINE#         232
  C:1188H         LINE#         234
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 26


  C:1196H         LINE#         236
  C:119DH         LINE#         237
  C:119FH         LINE#         238
  C:11A8H         LINE#         239
  C:11A8H         LINE#         240
  C:11B1H         LINE#         241
  C:11B3H         LINE#         243
  C:11B3H         LINE#         244
  C:11B6H         LINE#         245
  C:11B6H         LINE#         246
  -------         ENDPROC       TIMER1_ISR
  -------         PROC          UART1_ISR
  C:1400H         LINE#         249
  C:141DH         LINE#         252
  C:1420H         LINE#         253
  C:1420H         LINE#         254
  C:1424H         LINE#         255
  C:1428H         LINE#         256
  C:1432H         LINE#         257
  C:1434H         LINE#         258
  C:143CH         LINE#         259
  C:143CH         LINE#         260
  C:143EH         LINE#         261
  C:144DH         LINE#         262
  C:144DH         LINE#         263
  C:144DH         LINE#         264
  -------         ENDPROC       UART1_ISR
  -------         PROC          SCHEDULER_INIT
  C:001EH         LINE#         289
  C:001EH         LINE#         290
  C:001EH         LINE#         291
  C:0022H         LINE#         292
  -------         ENDPROC       SCHEDULER_INIT
  -------         PROC          SCHEDULER_RUN
  -------         DO            
  D:0022H         SYMBOL        i
  -------         DO            
  D:0023H         SYMBOL        now_time
  -------         ENDDO         
  -------         ENDDO         
  C:138BH         LINE#         295
  C:138BH         LINE#         296
  C:138BH         LINE#         299
  C:1396H         LINE#         300
  C:1396H         LINE#         301
  C:13A3H         LINE#         302
  C:13D5H         LINE#         303
  C:13D5H         LINE#         304
  C:13E9H         LINE#         305
  C:13FBH         LINE#         306
  C:13FBH         LINE#         307
  C:13FFH         LINE#         308
  -------         ENDPROC       SCHEDULER_RUN
  -------         PROC          DELAY750MS
  -------         DO            
  D:0007H         SYMBOL        i
  D:0006H         SYMBOL        j
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 27


  D:0005H         SYMBOL        k
  -------         ENDDO         
  C:1A8BH         LINE#         311
  C:1A8BH         LINE#         312
  C:1A8BH         LINE#         314
  C:1A8CH         LINE#         315
  C:1A8DH         LINE#         316
  C:1A8FH         LINE#         317
  C:1A91H         LINE#         318
  C:1A93H         LINE#         320
  C:1A93H         LINE#         322
  C:1A93H         LINE#         323
  C:1A95H         LINE#         325
  C:1A97H         LINE#         326
  C:1A99H         LINE#         327
  -------         ENDPROC       DELAY750MS
  -------         PROC          EEP_INIT
  C:198DH         LINE#         330
  C:198DH         LINE#         331
  C:198DH         LINE#         332
  C:199BH         LINE#         333
  -------         ENDPROC       EEP_INIT
  -------         PROC          MAIN
  C:1869H         LINE#         337
  C:1869H         LINE#         338
  C:1869H         LINE#         339
  C:186CH         LINE#         340
  C:186FH         LINE#         341
  C:1872H         LINE#         342
  C:1875H         LINE#         343
  C:1878H         LINE#         344
  C:1881H         LINE#         345
  C:1884H         LINE#         346
  C:1887H         LINE#         347
  C:188AH         LINE#         349
  C:188AH         LINE#         350
  C:188AH         LINE#         351
  C:188DH         LINE#         352
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        ?C_STARTUP
  C:1282H         SEGMENT       ?C_C51STARTUP
  I:00C5H         SEGMENT       ?STACK
  C:0000H         PUBLIC        ?C_STARTUP
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0080H         SYMBOL        IDATALEN
  C:1285H         SYMBOL        IDATALOOP
  N:0000H         SYMBOL        PBPSTACK
  N:0100H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:0000H         SYMBOL        PDATASTART
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 28


  N:0000H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00A0H         SYMBOL        PPAGE_SFR
  D:0081H         SYMBOL        SP
  C:1282H         SYMBOL        STARTUP1
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XDATALEN
  N:0000H         SYMBOL        XDATASTART
  C:0000H         LINE#         126
  C:1282H         LINE#         133
  C:1284H         LINE#         134
  C:1285H         LINE#         135
  C:1286H         LINE#         136
  C:1288H         LINE#         185
  C:128BH         LINE#         196
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        ?C?FPADD
  C:002DH         PUBLIC        ?C?FPADD
  C:0029H         PUBLIC        ?C?FPSUB
  -------         ENDMOD        ?C?FPADD

  -------         MODULE        ?C?FPMUL
  C:011EH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FPDIV
  C:0227H         PUBLIC        ?C?FPDIV
  -------         ENDMOD        ?C?FPDIV

  -------         MODULE        ?C?FPCMP
  C:02C6H         PUBLIC        ?C?FPCMP
  C:02C4H         PUBLIC        ?C?FPCMP3
  -------         ENDMOD        ?C?FPCMP

  -------         MODULE        ?C?FCAST
  C:0347H         PUBLIC        ?C?FCASTC
  C:0342H         PUBLIC        ?C?FCASTI
  C:033DH         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CASTF
  C:037BH         PUBLIC        ?C?CASTF
  -------         ENDMOD        ?C?CASTF

  -------         MODULE        PRINTF
  D:0022H         PUBLIC        ?_PRINTF?BYTE
  D:0022H         PUBLIC        ?_SPRINTF?BYTE
  C:0867H         PUBLIC        _PRINTF
  C:0861H         PUBLIC        _SPRINTF
  -------         ENDMOD        PRINTF

  -------         MODULE        ?C?FPROUND
  C:03FCH         PUBLIC        ?C?FPROUND
  -------         ENDMOD        ?C?FPROUND

BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 29


  -------         MODULE        ?C?FPCONVERT
  C:042BH         PUBLIC        ?C?FPCONVERT
  -------         ENDMOD        ?C?FPCONVERT

  -------         MODULE        ?C?FTNPWR
  C:055EH         PUBLIC        ?C?FTNPWR
  -------         ENDMOD        ?C?FTNPWR

  -------         MODULE        ?C?CLDPTR
  C:059EH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:05B7H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:05E4H         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:05F6H         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?IMUL
  C:0618H         PUBLIC        ?C?IMUL
  -------         ENDMOD        ?C?IMUL

  -------         MODULE        ?C?UIDIV
  C:062AH         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?SLDIV
  C:15A2H         PUBLIC        ?C?SLDIV
  -------         ENDMOD        ?C?SLDIV

  -------         MODULE        ?C?LNEG
  C:067FH         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

  -------         MODULE        ?C?ULCMP
  C:068DH         PUBLIC        ?C?ULCMP
  -------         ENDMOD        ?C?ULCMP

  -------         MODULE        ?C?LLDIDATA
  C:069EH         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

  -------         MODULE        ?C?LLDIDATA0
  C:06AAH         PUBLIC        ?C?LLDIDATA0
  -------         ENDMOD        ?C?LLDIDATA0

  -------         MODULE        ?C?LSTIDATA
  C:06B7H         PUBLIC        ?C?LSTIDATA
  -------         ENDMOD        ?C?LSTIDATA

  -------         MODULE        ?C?LSTPDATA
BL51 BANKED LINKER/LOCATER V6.22.4.0                                                  06/02/2025  19:35:43  PAGE 30


  C:06C3H         PUBLIC        ?C?LSTPDATA
  -------         ENDMOD        ?C?LSTPDATA

  -------         MODULE        ?C?PLDIIDATA
  C:06CFH         PUBLIC        ?C?PLDIIDATA
  -------         ENDMOD        ?C?PLDIIDATA

  -------         MODULE        ?C?CCASE
  C:06DFH         PUBLIC        ?C?CCASE
  -------         ENDMOD        ?C?CCASE

  -------         MODULE        ?C?ICALL
  C:0705H         PUBLIC        ?C?ICALL
  C:0709H         PUBLIC        ?C?ICALL2
  -------         ENDMOD        ?C?ICALL

  -------         MODULE        ?C?MEMSET
  C:070BH         PUBLIC        ?C?MEMSET
  -------         ENDMOD        ?C?MEMSET

  -------         MODULE        ?C?ULDIV
  C:0773H         PUBLIC        ?C?ULDIV
  -------         ENDMOD        ?C?ULDIV

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?BEEP?LED

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?RELAY?LED

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?MOTOR?LED

Program Size: data=177.4 xdata=59 code=6879
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
