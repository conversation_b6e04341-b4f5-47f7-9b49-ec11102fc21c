# 【2025】蓝桥杯单片机大模板使用说明 By：米醋电子工作室

## 新模板的诞生

### 旧模板存在的问题

在2024的模板中，我们采用如Seg_Slow_Down等减速变量来减速程序，实现简单的调度。而在2025的新模板中，我们进行了升级，采用了任务调度器进行分时调度。原因是前者存在一些系统性缺陷，以下是两者的具体区别：

#### 减速变量

我们的2024旧模板是在定时器中断里让减速变量每1毫秒自增，到达一定的数值后归零，在任务处理函数里判断对应的减速变量是否为真，为真则直接返回，为假则执行后面相关处理内容，以此来实现分时调用的目的。而这样就有一个逻辑上的问题，即有且只有减速变量等于0的时候后续处理代码才会被执行，而减速变量为0只能持续1毫秒，1毫秒后又在定时器中断里自增成1了，所以在while(1)里循环的几个处理函数的扫描必须小于1毫秒，否则将有概率跳过处理函数的本次执行。

 我们测试了常用的函数执行时间如下：

| 函数名称              | 所需时间                |
| --------------------- | ----------------------- |
| Rtc_Read()            | 125微秒                 |
| rd_temperature()      | 9767微秒                |
| AD_Read()             | 1460微秒                |
| Ultrasound_Read()     | 65535微秒+ 或 235 微秒+ |
| eeprom写10个数据      | 10832微秒               |
| printf("hello%d",num) | 8398微秒                |

那么思考如下的代码，当我们在Seg_Proc()函数里调用温度读取函数将会出现什么情况

```C
Seg_Proc()
{
  rd_temperature();
  //程序运行第1毫秒   此时Led_Slow_Down=192
  //程序运行第2毫秒   此时Led_Slow_Down=192
  ......
  //程序运行第8毫秒  此时Led_Slow_Down=199
  //程序运行第9毫秒  此时Led_Slow_Down=0 
  //程序运行第10毫秒 此时Led_Slow_Down=1 
}
while(1)
{
    Key_Proc();
    Seg_Proc();    //当退出该函数时Led_Slow_Down将为1，而其中归0了一次
    Led_Proc();    //Led_Slow_Down=1,该函数直接返回
}
/* 定时器1中断服务程序 */
void Timer1_Isr(void) interrupt 3
{
 if(++Led_Slow_Down==200)Led_Slow_Down=0;		
}
```

显而易见当出现这种情况的时候，Led_Proc()函数跳过了一次执行，这在有的时候可能是致命的，因为它的刷新时间本次由200毫秒百变成400毫秒。

#### 新版调度器

我们的新模板采用了全局变量uwTick来计时，在定时器中断里每一毫秒自增一，调度器通过检查每个任务的上次运行时间和当前时间，来决定是否执行任务，这种设计可以确保任务按照预定的周期执行。只要满足了运行的条件，该任务就可以被执行。

举个例子，旧版的相当于，你告诉一个人，20：00可以吃东西，那么如果他有事错过了20：00，20：01再去看时钟，此时就不满足20：00的条件的，那么吃东西这个任务就不会得到执行。而新模板相当于，你告诉他，20：00以后可以吃东西，那么哪怕错过了20：00，20：01再去看时钟，也是满足20：00以后这个条件的，那么吃东西这个任务就会得到执行，就不会出现跳过执行的情况了。

### 更换新模板的必要性

近年来蓝桥杯单片机组难度逐年增大，题目开始由单纯的外设叠加向数据解析和简单算法处理转变，各种要求限制也越来越多。比如15届省赛题要求数码管刷新率≤0.1秒，如果此时调用温度读取函数，将耗时10毫秒，那么此时跳过某处理函数的概率将高达10％。幸好15届省赛真题只用到了频率测量，没有出现大面积因为该bug扣分的情况。但这应当引起我们的警觉，该bug在25届有暴雷的风险，更换新模板是必要的。



## 新模板

### 新模板的测试

新模板可全模块同时编译通过同时运行，可直接下载上电测试。上电默认时间界面，默认显示11.11.11。按下S4切换到温度显示界面，显示温度(中值滤波后的值)。再次按下S4切换到超声波测距界面，数码管显示测量距离(滑动平均滤波后的值)。再次按下S4切换到AD显示界面，显示电位器RB2电压，AD读取地址改为0X41则显示光敏电阻电压。再次按下S4切换到频率显示界面，应用跳线帽短接SIGNAL和P34，频率典型最大值为3万上下。再次按下S4切换到eeprom显示界面，显示Eep_Read数组，显示8个0表示写入或读取失败，显示12345678表示写入成功，读取成功。再次按下S4切换回时间显示界面。pwm调光共10档可调，默认5档，按下按键S8亮度减一，按下按键S9亮度加一。

### 新模板的内存分配

实际上iap15单片机的data只有128个字节，在引入串口数据解析，和格式化输出函数printf等以后是很容易内存不够导致编译不通过的。要解决这个问题就要了解iap15单片机的几种存储介质的大小和特性等，详细特性见下表：(访问速度以连续写入30个char数据的时间计算)



| 名称  | 大小    | 访问速度 | 备注                                 |
| ----- | ------- | -------- | ------------------------------------ |
| data  | 128字节 | 33微秒   | data为直接寻址                       |
| idata | 256字节 | 33微秒   | idata为间接寻址，使用不要超过128字节 |
| xdata | 64k字节 | 50微秒   | xdata为外部存储，访问速度较慢        |
| pdata | 128字节 | 38微秒   | pdata为xdata的当前页，是xdata的子集  |

分析上表可知，data和idata的读写速度最快，编写代码时推荐优先写满data的128个字节，再考虑idata的其中的128个字节，实在不够用再考虑pdata,外部存储的使用可能会降低系统运行速度，且pdata和xdata作为全局变量使用时不默认为0，需要初始化。

### 新模板的Led

新模板的Led由原来每1毫秒刷新一位改为调用一次Led_Disp()函数即连续刷新8个Led灯。新增了Led_Off()函数来熄灭所有Led以便于进行pwm调光。代码如下：

```c
idata  unsigned char temp_1 = 0x00;
idata  unsigned char temp_old_1 = 0xff;

void Led_Disp(unsigned char *ucLed)
{
   temp_1=0x00;
    /*此处用8个或运算而不是for循环是为了加快函数执行速度*/
   temp_1 = (ucLed[0] << 0) | (ucLed[1] << 1) | (ucLed[2] << 2) | (ucLed[3] << 3) |
         (ucLed[4] << 4) | (ucLed[5] << 5) | (ucLed[6] << 6) | (ucLed[7] << 7);
  if (temp_1 != temp_old_1)
  {
    P0 = ~temp_1;
    P2 = (P2 & 0x1f) | 0x80;
    P2 &= 0x1f;
    temp_old_1 = temp_1;
  }
}
void Led_Off()
{
	P0 = 0xff;                 //关闭所有led灯
    P2 = (P2 & 0x1f) | 0x80;   //打开led锁存器
    P2 &= 0x1f;                //关闭锁存器
	temp_old_1=0x00;           //更新Led_Old,避免下次不刷新
}
```

### 新模板的数码管

新模板的数码管将Seg_Point数组舍去，仅通过Seg_Buf数组判断小数点显示。其原理是在原来数字的基础上加上一个符号，比如逗号，然后在刷新数码管的时候判断Seg_Buf该位数字的大小，如果超过20，则加了逗号，表示有小数点，则点亮小数点并且数码管显示该位减去逗号。如果没超过20，则关闭小数点，直接显示数字。关键代码如下：

```c
Seg_Buf[0]=1;      //数码管第0位显示1.小数点熄灭
Seg_Buf[1]=2+',';  //数码管第1位显示2.（小数点点亮）
// 数码管显示处理
if (Seg_Buf[Seg_Pos] > 20)
Seg_Disp(Seg_Pos, Seg_Buf[Seg_Pos] - ',', 1); // 带小数点
else
Seg_Disp(Seg_Pos, Seg_Buf[Seg_Pos], 0); // 无小数点
```

### 新模板的ds1302

 新模板在旧模板的基础上增加了中断的处理，即在读取前关闭总中断，读取后开启总中断，避免在读取时定时器1中断打断ds1302时序造成数据错误。因为调用整个Rtc_Read()函数也不过125微秒，相比于它几百毫秒的调用周期和定时器1的1毫秒中断频率来算，基本不会对数码管刷新等产生影响，故采用此方法解决中断打断时序的问题。

```c
void Read_Rtc(unsigned char *ucRtc)
{
	unsigned char i;
	unsigned temp;
	EA=0;  //关闭总中断
	for (i = 0; i < 3; i++)
	{
		temp = Read_Ds1302_Byte(0x85 - 2 * i);
		ucRtc[i] = temp / 16 * 10 + temp % 16;
	}
	EA=1;  //打开总中断
}
```

### 新模板的DS18B20

DS18B20作为单总线通信的模块，没有硬件单总线外设，靠延时实现的时序当然也存在被定时器中断打断而数据读取错误的现象。但rd_temperature()函数调用一次9767微秒，如果继续采用关闭中断的方式解决问题将带来毁灭性的灾难，数码管会出现明显的亮暗不均匀和闪烁，  系统时钟uwTick的计时也将受到影响。所以这种系统性的问题没办法避免，只能对拿到的错误的数据做后续处理，也就是滤波。新模板采用了中值滤波来滤除错误的数据，代码如下：

```c
#define N 10 // 滤波窗口大小
pdata int data_array[N]={0}; // 存储窗口内的数据
pdata int index = 0; // 当前数据的索引
// 中值滤波器函数
// 参数：new_data - 新的输入数据
// 返回值：滤波后的数据
int Median_Filter(int new_data) 
{
	idata  int sorted_data[N];
    idata     int i, j, temp;
    // 复制数据到临时数组
    for (i = 0; i < N; i++) {
        sorted_data[i] = data_array[i];
    }
    // 冒泡排序
    for (i = 0; i < N - 1; i++) 
		{
      for (j = 0; j < N - i - 1; j++) 
			{
            if (sorted_data[j] > sorted_data[j + 1]) 
						{
                // 交换元素位置
                temp = sorted_data[j];
                sorted_data[j] = sorted_data[j + 1];
                sorted_data[j + 1] = temp;
            }
      }
    }
    data_array[index] = new_data; // 存储新数据
    index = (index + 1) % N; // 更新索引
    return sorted_data[N / 2]; // 返回中值
}

/*温度读取函数*/
void Get_temputure(void)
{
 unsigned int temp =  rd_temperature() * 100;
 temputure_100x= Median_Filter(temp);
}
```

### 新模板的超声波

超声波依然存在被中断打断导致读取错误的问题，同样因为其高达6毫秒多的执行时间而不适合关闭中断避免干扰，依然采用胡阿东平均滤波的方式来消除错误数据，代码如下：

```c
pdata int data_array_2[N]={0}; // 存储窗口内的数据
pdata int sum = 0; // 存储窗口内数据的和
pdata int index_2 = 0; // 当前数据的索引
// 滑动平均滤波器函数
// 参数：new_data - 新的输入数据
// 返回值：滤波后的数据
unsigned  int Moving_Average_Filter(unsigned  int new_data) 
{
    sum -=  data_array_2[index_2]; // 减去窗口中被替换的数据
    data_array_2[index_2] = new_data; // 存储新数据
    sum += new_data; // 计算新的和
    index_2 = (index_2 + 1) % N; // 更新索引
    return sum / N; // 返回平均值
}
/*超声波测距读取函数*/
void Get_distance(void)
{
  	unsigned int temp = Ut_Wave_Data();
	  distance= Moving_Average_Filter(temp);
}
```

### 是否总是需要滤波

其实也不是所有的情况都需要加入滤波的，只有当数据跳变频繁的时候才需要加入滤波。一般是需要pwm进行led调光的时候，Led_Disp()函数需要放到中断里面执行，以避免闪烁的问题。而Led_Disp()函数执行的时间比较久，会加长中断时间从而导致中断频繁干扰时序，此时需要加入滤波。而不需要pwm调光的时候，Led_Disp()可以放到Led_Proc()里执行，此时定时器中断执行速度比较快，一般不会对时钟和温度超声波时序产生特别严重的干扰。

### 关于定时器1中断

因为定时器1中断会打断其他模块时序，所以定时器1中断要快进快出。在不需要pwm进行led调光的时候需要把Led_Disp()函数放到Led_Proc()函数里执行，以降低中断执行时间，然后在中断函数里尽量避免乘法，除法，取余运算的耗时的操作。
